import 'package:flutter/foundation.dart';
import 'integrity_checker.dart';
import 'security_utils.dart';

/// مهيئ نظام الأمان
class SecurityInitializer {
  /// تهيئة نظام الأمان
  static Future<bool> initialize() async {
    try {
      // التحقق من سلامة التطبيق
      final isIntegrityValid = await IntegrityChecker.verifyAppIntegrity();
      
      // التحقق من صحة التشفير
      final isEncryptionValid = SecurityUtils.verifyAppIntegrity();
      
      // في بيئة التطوير، نسمح بالتشغيل حتى لو فشلت بعض الفحوصات
      if (kDebugMode) {
        debugPrint('🔒 حالة سلامة التطبيق: $isIntegrityValid');
        debugPrint('🔒 حالة التشفير: $isEncryptionValid');
        return true;
      }
      
      return isIntegrityValid && isEncryptionValid;
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة نظام الأمان: $e');
      return false;
    }
  }
  
  /// التحقق من صلاحية المفتاح
  static bool verifyApiKey() {
    try {
      final apiKey = SecurityUtils.getApiKey();
      return apiKey.isNotEmpty && apiKey.length > 20;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من صلاحية المفتاح: $e');
      return false;
    }
  }
}