import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../utils/security_utils.dart';

/// خدمة البحث في الإنترنت للذكاء الاصطناعي
class SearchService {
  /// مفتاح API للبحث
  static final String _searchApiKey = SecurityUtils.getSearchApiKey();
  
  /// معرف محرك البحث المخصص
  static const String _searchEngineId = 'e06b6b250b6c34eab';
  
  /// البحث في الإنترنت
  static Future<SearchResult> searchWeb(String query) async {
    try {
      final encodedQuery = Uri.encodeComponent(query);
      final url = Uri.parse(
        'https://www.googleapis.com/customsearch/v1?key=$_searchApiKey&cx=$_searchEngineId&q=$encodedQuery',
      );
      
      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return _parseSearchResults(data);
      } else {
        debugPrint('❌ خطأ في البحث: ${response.statusCode}');
        return SearchResult.error('فشل البحث: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ استثناء في البحث: $e');
      return SearchResult.error('حدث خطأ أثناء البحث');
    }
  }
  
  /// البحث عن معلومات حديثة
  static Future<SearchResult> searchRecentInfo(String query) async {
    try {
      // إضافة كلمات مفتاحية للحصول على معلومات حديثة
      final enhancedQuery = '$query latest information';
      return await searchWeb(enhancedQuery);
    } catch (e) {
      return SearchResult.error('فشل البحث عن معلومات حديثة');
    }
  }
  
  /// البحث عن تعريفات
  static Future<SearchResult> searchDefinition(String term) async {
    try {
      final query = 'define $term meaning';
      return await searchWeb(query);
    } catch (e) {
      return SearchResult.error('فشل البحث عن التعريف');
    }
  }
  
  /// تحليل نتائج البحث
  static SearchResult _parseSearchResults(Map<String, dynamic> data) {
    try {
      final items = data['items'] as List?;
      
      if (items == null || items.isEmpty) {
        return SearchResult.empty();
      }
      
      final results = <SearchItem>[];
      
      for (final item in items.take(5)) {
        results.add(
          SearchItem(
            title: item['title'] as String? ?? '',
            link: item['link'] as String? ?? '',
            snippet: item['snippet'] as String? ?? '',
          ),
        );
      }
      
      return SearchResult(
        items: results,
        totalResults: data['searchInformation']?['totalResults'] as String? ?? '0',
      );
    } catch (e) {
      debugPrint('❌ خطأ في تحليل نتائج البحث: $e');
      return SearchResult.error('فشل تحليل نتائج البحث');
    }
  }
  
  /// استخراج المعلومات المهمة من نتائج البحث
  static String extractRelevantInfo(SearchResult result, String query) {
    if (result.error != null) {
      return 'لم أتمكن من العثور على معلومات: ${result.error}';
    }
    
    if (result.items.isEmpty) {
      return 'لم أجد معلومات متعلقة بـ "$query"';
    }
    
    final relevantItems = result.items.take(3);
    final info = StringBuffer();
    
    info.writeln('وجدت المعلومات التالية:');
    
    for (final item in relevantItems) {
      info.writeln('\n• ${item.title}');
      info.writeln('  ${item.snippet}');
    }
    
    return info.toString();
  }
}

/// نتيجة البحث
class SearchResult {
  final List<SearchItem> items;
  final String totalResults;
  final String? error;
  
  SearchResult({
    required this.items,
    required this.totalResults,
    this.error,
  });
  
  factory SearchResult.empty() {
    return SearchResult(
      items: [],
      totalResults: '0',
    );
  }
  
  factory SearchResult.error(String errorMessage) {
    return SearchResult(
      items: [],
      totalResults: '0',
      error: errorMessage,
    );
  }
}

/// عنصر بحث
class SearchItem {
  final String title;
  final String link;
  final String snippet;
  
  SearchItem({
    required this.title,
    required this.link,
    required this.snippet,
  });
}