import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'security_utils.dart';

/// فاحص سلامة التطبيق
class IntegrityChecker {
  /// التحقق من سلامة التطبيق
  static Future<bool> verifyAppIntegrity() async {
    try {
      // التحقق من سلامة التطبيق
      final isSecure = await _checkAppSecurity();
      final isGenuine = await _checkGenuineInstallation();
      final isNotTampered = await _checkTamperingAttempts();
      
      return isSecure && isGenuine && isNotTampered;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من سلامة التطبيق: $e');
      return false;
    }
  }
  
  /// التحقق من أمان التطبيق
  static Future<bool> _checkAppSecurity() async {
    try {
      // التحقق من وجود أدوات التطوير
      final isRooted = await _checkForRootJailbreak();
      
      // التحقق من تشغيل التطبيق في بيئة محاكاة
      final isEmulator = await _checkForEmulator();
      
      // في بيئة التطوير، نسمح بالتشغيل في المحاكي
      if (kDebugMode) {
        return !isRooted;
      }
      
      return !isRooted && !isEmulator;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من أمان التطبيق: $e');
      return false;
    }
  }
  
  /// التحقق من أصالة التثبيت
  static Future<bool> _checkGenuineInstallation() async {
    try {
      // الحصول على معلومات التطبيق
      final packageInfo = await PackageInfo.fromPlatform();
      
      // التحقق من اسم الحزمة
      final isValidPackage = packageInfo.packageName == 'com.edutrack.app';
      
      // في بيئة التطوير، نسمح بأي اسم حزمة
      if (kDebugMode) {
        return true;
      }
      
      return isValidPackage;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من أصالة التثبيت: $e');
      return true; // نعود بـ true في حالة الخطأ لتجنب منع المستخدمين الشرعيين
    }
  }
  
  /// التحقق من محاولات العبث
  static Future<bool> _checkTamperingAttempts() async {
    try {
      // التحقق من سلامة الملفات الحساسة
      final isFilesIntact = await _checkFilesIntegrity();
      
      return isFilesIntact;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من محاولات العبث: $e');
      return true; // نعود بـ true في حالة الخطأ لتجنب منع المستخدمين الشرعيين
    }
  }
  
  /// التحقق من وجود روت أو جيلبريك
  static Future<bool> _checkForRootJailbreak() async {
    try {
      if (Platform.isAndroid) {
        // التحقق من وجود ملفات الروت الشائعة
        final paths = [
          '/system/app/Superuser.apk',
          '/system/xbin/su',
          '/system/bin/su',
          '/sbin/su',
          '/system/su',
          '/system/bin/.ext/.su',
        ];
        
        for (final path in paths) {
          if (await File(path).exists()) {
            return true;
          }
        }
        
        return false;
      } else if (Platform.isIOS) {
        // التحقق من وجود تطبيقات الجيلبريك الشائعة
        final paths = [
          '/Applications/Cydia.app',
          '/Library/MobileSubstrate/MobileSubstrate.dylib',
          '/bin/bash',
          '/usr/sbin/sshd',
          '/etc/apt',
          '/private/var/lib/apt/',
        ];
        
        for (final path in paths) {
          if (await File(path).exists()) {
            return true;
          }
        }
        
        return false;
      }
      
      return false;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من الروت: $e');
      return false;
    }
  }
  
  /// التحقق من تشغيل التطبيق في محاكي
  static Future<bool> _checkForEmulator() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        
        // التحقق من خصائص المحاكي
        final isEmulator = androidInfo.isPhysicalDevice == false ||
            androidInfo.product.contains('sdk') ||
            androidInfo.fingerprint.contains('generic') ||
            androidInfo.model.contains('google_sdk') ||
            androidInfo.model.contains('Emulator') ||
            androidInfo.model.contains('Android SDK');
            
        return isEmulator;
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        
        // التحقق من خصائص المحاكي
        final isSimulator = !iosInfo.isPhysicalDevice;
        
        return isSimulator;
      }
      
      return false;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من المحاكي: $e');
      return false;
    }
  }
  
  /// التحقق من سلامة الملفات الحساسة
  static Future<bool> _checkFilesIntegrity() async {
    try {
      // في التطبيق الحقيقي، يمكن حساب قيم التجزئة للملفات المهمة ومقارنتها
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من سلامة الملفات: $e');
      return true;
    }
  }
}