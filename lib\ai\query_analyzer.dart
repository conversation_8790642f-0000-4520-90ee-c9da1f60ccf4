import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import '../utils/security_utils.dart';

/// محلل الاستعلامات المتقدم
class QueryAnalyzer {
  /// نموذج الذكاء الاصطناعي للتحليل
  static GenerativeModel? _analyzerModel;
  
  /// تهيئة المحلل
  static void initialize() {
    try {
      _analyzerModel = GenerativeModel(
        model: 'gemini-1.5-flash',
        apiKey: SecurityUtils.getApiKey(),
        generationConfig: GenerationConfig(
          temperature: 0.2, // درجة حرارة منخفضة للحصول على نتائج أكثر دقة
          maxOutputTokens: 1024,
        ),
      );
      debugPrint('✅ تم تهيئة محلل الاستعلامات');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة محلل الاستعلامات: $e');
    }
  }
  
  /// تحليل استعلام المستخدم
  static Future<QueryAnalysis> analyzeQuery(String query) async {
    try {
      if (_analyzerModel == null) {
        initialize();
      }
      
      // إنشاء البرومبت للتحليل
      final prompt = _createAnalysisPrompt(query);
      
      // إرسال للذكاء الاصطناعي
      final content = [Content.text(prompt)];
      final response = await _analyzerModel!.generateContent(content);
      final responseText = response.text ?? '';
      
      // تحليل الاستجابة
      return _parseAnalysisResponse(responseText, query);
    } catch (e) {
      debugPrint('❌ خطأ في تحليل الاستعلام: $e');
      return _fallbackAnalysis(query);
    }
  }
  
  /// إنشاء برومبت التحليل
  static String _createAnalysisPrompt(String query) {
    return '''
حلل هذا الاستعلام وصنفه بدقة:
"$query"

أجب بـ JSON فقط بالتنسيق التالي:
{
  "queryType": "question|action|chat|search|definition",
  "intent": "get_info|perform_task|casual_chat|search_web|define_term",
  "category": "educational|technical|administrative|personal|general",
  "complexity": "simple|medium|complex",
  "requiresSearch": true|false,
  "requiresAction": true|false,
  "entities": ["entity1", "entity2"],
  "keywords": ["keyword1", "keyword2"],
  "sentiment": "positive|neutral|negative",
  "language": "arabic|english|mixed",
  "confidence": 0.95
}
''';
  }
  
  /// تحليل استجابة التحليل
  static QueryAnalysis _parseAnalysisResponse(String response, String originalQuery) {
    try {
      // استخراج JSON من الاستجابة
      final jsonStart = response.indexOf('{');
      final jsonEnd = response.lastIndexOf('}') + 1;
      
      if (jsonStart >= 0 && jsonEnd > jsonStart) {
        final jsonStr = response.substring(jsonStart, jsonEnd);
        final data = jsonDecode(jsonStr) as Map<String, dynamic>;
        
        return QueryAnalysis(
          originalQuery: originalQuery,
          queryType: data['queryType'] as String? ?? 'chat',
          intent: data['intent'] as String? ?? 'casual_chat',
          category: data['category'] as String? ?? 'general',
          complexity: data['complexity'] as String? ?? 'simple',
          requiresSearch: data['requiresSearch'] as bool? ?? false,
          requiresAction: data['requiresAction'] as bool? ?? false,
          entities: _parseStringList(data['entities']),
          keywords: _parseStringList(data['keywords']),
          sentiment: data['sentiment'] as String? ?? 'neutral',
          language: data['language'] as String? ?? 'arabic',
          confidence: (data['confidence'] as num?)?.toDouble() ?? 0.8,
        );
      }
      
      return _fallbackAnalysis(originalQuery);
    } catch (e) {
      debugPrint('❌ خطأ في تحليل استجابة التحليل: $e');
      return _fallbackAnalysis(originalQuery);
    }
  }
  
  /// تحليل قائمة النصوص
  static List<String> _parseStringList(dynamic value) {
    if (value is List) {
      return value.map((e) => e.toString()).toList();
    }
    return [];
  }
  
  /// تحليل احتياطي بسيط
  static QueryAnalysis _fallbackAnalysis(String query) {
    final lowerQuery = query.toLowerCase();
    
    // تحديد نوع الاستعلام بناءً على الكلمات المفتاحية
    String queryType = 'chat';
    String intent = 'casual_chat';
    bool requiresSearch = false;
    bool requiresAction = false;
    
    // التحقق من الأسئلة
    if (lowerQuery.contains('ما هو') || 
        lowerQuery.contains('كيف') || 
        lowerQuery.contains('متى') || 
        lowerQuery.contains('أين') ||
        lowerQuery.contains('لماذا') ||
        lowerQuery.contains('هل')) {
      queryType = 'question';
      intent = 'get_info';
    }
    
    // التحقق من الإجراءات
    if (lowerQuery.contains('أضف') || 
        lowerQuery.contains('احذف') || 
        lowerQuery.contains('عدل') || 
        lowerQuery.contains('غير') ||
        lowerQuery.contains('أنشئ')) {
      queryType = 'action';
      intent = 'perform_task';
      requiresAction = true;
    }
    
    // التحقق من البحث
    if (lowerQuery.contains('ابحث') || 
        lowerQuery.contains('جد لي') || 
        lowerQuery.contains('اعثر على')) {
      queryType = 'search';
      intent = 'search_web';
      requiresSearch = true;
    }
    
    // استخراج الكلمات المفتاحية
    final words = query.split(' ')
        .where((word) => word.length > 3)
        .take(5)
        .toList();
    
    return QueryAnalysis(
      originalQuery: query,
      queryType: queryType,
      intent: intent,
      category: 'general',
      complexity: 'simple',
      requiresSearch: requiresSearch,
      requiresAction: requiresAction,
      entities: [],
      keywords: words,
      sentiment: 'neutral',
      language: 'arabic',
      confidence: 0.7,
    );
  }
}

/// تحليل الاستعلام
class QueryAnalysis {
  final String originalQuery;
  final String queryType;
  final String intent;
  final String category;
  final String complexity;
  final bool requiresSearch;
  final bool requiresAction;
  final List<String> entities;
  final List<String> keywords;
  final String sentiment;
  final String language;
  final double confidence;
  
  QueryAnalysis({
    required this.originalQuery,
    required this.queryType,
    required this.intent,
    required this.category,
    required this.complexity,
    required this.requiresSearch,
    required this.requiresAction,
    required this.entities,
    required this.keywords,
    required this.sentiment,
    required this.language,
    required this.confidence,
  });
  
  /// هل الاستعلام سؤال؟
  bool get isQuestion => queryType == 'question';
  
  /// هل الاستعلام إجراء؟
  bool get isAction => queryType == 'action';
  
  /// هل الاستعلام دردشة؟
  bool get isChat => queryType == 'chat';
  
  /// هل الاستعلام بحث؟
  bool get isSearch => queryType == 'search';
  
  /// هل الاستعلام تعريف؟
  bool get isDefinition => queryType == 'definition';
  
  /// الحصول على نوع الاستجابة المناسب
  String get responseType {
    if (isChat) return 'conversational';
    if (isQuestion) return 'informative';
    if (isAction) return 'actionable';
    if (isSearch) return 'search_results';
    if (isDefinition) return 'definition';
    return 'general';
  }
  
  @override
  String toString() {
    return 'QueryAnalysis{type: $queryType, intent: $intent, requiresSearch: $requiresSearch, requiresAction: $requiresAction}';
  }
}